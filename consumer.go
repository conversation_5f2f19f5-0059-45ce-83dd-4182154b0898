package main

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
)

type Message struct {
	Key       []byte
	Value     []byte
	Topic     string
	Partition int32
	Offset    int64
}

type Message<PERSON><PERSON><PERSON> func(key, value []byte) error

type BatchMessageHandler func(message []Message) error

type Consumer interface {
	Consume(topic string, handler MessageHandler) error
	ConsumeBatch(topic string, handler BatchMessageHandler) error
	Close()
}

type KafkaConsumer struct {
	consumer      *kafka.Consumer
	done          chan bool
	wg            sync.WaitGroup
	handler       MessageHandler
	batchHandler  BatchMessageHandler
	topic         string
	batchSize     int
	batchTimeOut  time.Duration
	messageBatch  []Message
	lastBatchTime time.Time
	mu            sync.Mutex
}

type ConsumerConfig struct {
	Brokers            string
	GroupID            string
	ClientID           string
	EnableAutoCommit   bool
	AutoCommitInterval int
	BatchSize          int
	BatchTimeOut       time.Duration
}

// NewKafkaConsumer creates a new Kafka consumer instance with the specified configuration
func NewKafkaConsumer(config ConsumerConfig) (*KafkaConsumer, error) {
	c, err := kafka.NewConsumer(&kafka.ConfigMap{
		"bootstrap.servers":       config.Brokers,
		"group.id":                config.GroupID,
		"client.id":               config.ClientID,
		"enable.auto.commit":      config.EnableAutoCommit,
		"auto.commit.interval.ms": config.AutoCommitInterval,
		"broker.address.family":   "v4",
		"auto.offset.reset":       "earliest",
	})

	if err != nil {
		return nil, fmt.Errorf("failed to create consumer: %w", err)
	}

	kc := &KafkaConsumer{
		consumer:      c,
		done:          make(chan bool),
		batchSize:     config.BatchSize,
		batchTimeOut:  config.BatchTimeOut,
		messageBatch:  make([]Message, 0, config.BatchSize),
		lastBatchTime: time.Now(),
	}

	slog.Info("Kafka consumer initialized")
	return kc, nil
}

// Consume starts consuming messages from a topic one at a time using the provided handler
func (kc *KafkaConsumer) Consume(topic string, handler MessageHandler) error {
	if handler == nil {
		return fmt.Errorf("message handler can't be nil")
	}

	kc.handler = handler
	kc.topic = topic
	err := kc.consumer.SubscribeTopics([]string{kc.topic}, nil)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to topic %s: %w", topic, err)
	}

	kc.start()
	slog.Info("Started consuming from topic", "topic", topic)
	return nil
}

// ConsumeBatch starts consuming messages from a topic in batches using the provided batch handler
func (kc *KafkaConsumer) ConsumeBatch(topic string, handler BatchMessageHandler) error {
	if handler == nil {
		return fmt.Errorf("batch message handler can't be nil")
	}

	kc.batchHandler = handler
	kc.topic = topic
	err := kc.consumer.SubscribeTopics([]string{kc.topic}, nil)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to topic %s: %w", topic, err)
	}

	kc.start()
	slog.Info("Started batch consuming from topic", "topic", topic)
	return nil
}

// start initializes the consumer by starting the message consumption goroutine
func (kc *KafkaConsumer) start() {
	kc.wg.Add(1)
	go kc.consumeMessages()
}

// consumeMessages polls for messages from Kafka and processes them based on the configured handler type
func (kc *KafkaConsumer) consumeMessages() {
	defer kc.wg.Done()

	slog.Info("Starting the kafka consumer for topic", "topic", kc.topic)

	if kc.batchHandler != nil {
		kc.wg.Add(1)
		go kc.batchTimeOutChecker()
	}

	for {
		select {
		case <-kc.done:
			if kc.batchHandler != nil {
				kc.processBatch()
			}
			slog.Info("Kafka consumer shutting down...")
			return
		default:
			ev := kc.consumer.Poll(100)
			if ev == nil {
				continue
			}

			switch e := ev.(type) {
			case *kafka.Message:
				if kc.batchHandler != nil {
					kc.addToBatch(e)
				} else {
					kc.processMessage(e)
				}
			case kafka.Error:
				kc.handleKafkaError(e)
			default:
				//ignore
			}

		}
	}
}

// processMessage handles individual messages by calling the registered message handler
func (kc *KafkaConsumer) processMessage(msg *kafka.Message) {
	if msg.TopicPartition.Error != nil {
		slog.Error("Message error from topic", 
			"topic", *msg.TopicPartition.Topic,
			"offset", msg.TopicPartition.Offset,
			"error", msg.TopicPartition.Error)
		return
	}

	if kc.handler != nil {
		kc.handler(msg.Key, msg.Value)
	}
}

// addToBatch adds a message to the current batch and processes the batch if it reaches the configured size
func (kc *KafkaConsumer) addToBatch(msg *kafka.Message) {
	if msg.TopicPartition.Error != nil {
		slog.Error("Error message from topic", 
			"topic", *msg.TopicPartition.Topic,
			"partition", msg.TopicPartition.Partition,
			"error", msg.TopicPartition.Error)
		return
	}

	kc.mu.Lock()
	defer kc.mu.Unlock()

	message := Message{
		Key:       msg.Key,
		Value:     msg.Value,
		Topic:     *msg.TopicPartition.Topic,
		Partition: msg.TopicPartition.Partition,
		Offset:    int64(msg.TopicPartition.Offset),
	}

	kc.messageBatch = append(kc.messageBatch, message)

	if len(kc.messageBatch) >= kc.batchSize {
		kc.processBatch()
	}
}

// processBatch sends the current batch of messages to the batch handler and resets the batch
func (kc *KafkaConsumer) processBatch() {
	if len(kc.messageBatch) == 0 {
		return
	}

	batch := make([]Message, len(kc.messageBatch))
	copy(batch, kc.messageBatch)
	kc.messageBatch = kc.messageBatch[:0]
	kc.lastBatchTime = time.Now()

	if kc.batchHandler != nil {
		kc.batchHandler(batch)
	}
}

// batchTimeOutChecker monitors batch timeout and processes incomplete batches when they exceed the timeout
func (kc *KafkaConsumer) batchTimeOutChecker() {
	defer kc.wg.Done()

	ticker := time.NewTicker(kc.batchTimeOut / 2)
	defer ticker.Stop()

	for {
		select {
		case <-kc.done:
			return
		case <-ticker.C:
			kc.mu.Lock()
			if len(kc.messageBatch) > 0 && time.Since(kc.lastBatchTime) >= kc.batchTimeOut {
				kc.processBatch()
			}
			kc.mu.Unlock()
		}
	}
}

// handleKafkaError logs Kafka consumer errors and checks for fatal errors
func (kc *KafkaConsumer) handleKafkaError(err kafka.Error) {
	slog.Error("Kafka consumer error", "error", err, "code", err.Code())

	if err.IsFatal() {
		slog.Error("FATAL kafka consumer error - may need to restart", "error", err)
	}
}

// Close gracefully shuts down the consumer by closing connections and waiting for goroutines to finish
func (kc *KafkaConsumer) Close() {
	slog.Info("Closing kafka consumer....")

	close(kc.done)

	err := kc.consumer.Close()
	if err != nil {
		slog.Error("Error closing the consumer", "error", err)
	}

	kc.wg.Wait()
	slog.Info("Kafka consumer shutdown complete")
}

// UnmarshalJSON is a helper function to unmarshal JSON messages into a provided interface
func UnmarshalJSON(data []byte, v interface{}) error {
	return json.Unmarshal(data, v)
}
